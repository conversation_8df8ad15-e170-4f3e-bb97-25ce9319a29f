<?php require("components/header.php");?>

        <header class="site-header d-flex flex-column justify-content-center align-items-center">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-5 col-12">
                        <h2 class="text-white">Contact</h2>
                    </div>
                </div>
            </div>
        </header>

        <section class="section-padding section-bg">
            <div class="container">
                <div class="row">
                    <div class="col-lg-12 col-12">
                        <h3 class="mb-4 pb-2">We'd love to hear from you</h3>
                    </div>

                    <div class="col-lg-6 col-12">
                        <?php if (isset($_GET['success'])): ?>
                            <div class="alert alert-success" role="alert">
                                Your message has been sent successfully!
                            </div>
                        <?php elseif (isset($_GET['error'])): ?>
                            <div class="alert alert-danger" role="alert">
                                Sorry, your message could not be sent. Please try again.
                            </div>
                        <?php endif; ?>
                        <form action="mail.php" method="post" class="custom-form contact-form" role="form">
                            <div class="row">
                                <div class="col-lg-6 col-md-6 col-12">
                                    <div class="form-floating">
                                        <input type="text" name="name" id="name" class="form-control" placeholder="Name"
                                            required="" />

                                        <label for="floatingInput">Name</label>
                                    </div>
                                </div>

                                <div class="col-lg-6 col-md-6 col-12">
                                    <div class="form-floating">
                                        <input type="email" name="email" id="email" pattern="[^ @]*@[^ @]*"
                                            class="form-control" placeholder="Email address" required="" />

                                        <label for="floatingInput">Email address</label>
                                    </div>
                                </div>

                                <div class="col-lg-12 col-12">
                                    <div class="form-floating">
                                        <input type="text" name="subject" id="name" class="form-control"
                                            placeholder="Name" required="" />

                                        <label for="floatingInput">Subject</label>
                                    </div>

                                    <div class="form-floating">
                                        <textarea class="form-control" id="message" name="message"
                                            placeholder="Tell me about the project"></textarea>

                                        <label for="floatingTextarea">Tell me about the project</label>
                                    </div>
                                </div>

                                <div class="col-lg-4 col-12 ms-auto">
                                    <button type="submit" class="form-control">Submit</button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div class="col-lg-5 col-12 mx-auto mt-5 mt-lg-0">
                        <iframe class="google-map"
                            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d94831.82542682631!2d-88.17363238657535!3d42.03257449166478!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x880faf88436224e5%3A0x43bd31cd678f7351!2sSchaumburg%2C%20IL%2C%20USA!5e0!3m2!1sen!2s!4v1756290853555!5m2!1sen!2s"
                            width="100%" height="250" style="border: 0" allowfullscreen="" loading="lazy"
                            referrerpolicy="no-referrer-when-downgrade"></iframe>

                        <h5 class="mt-4 mb-2">Head Office</h5>

                        <p>Schaumburg, Illinois, USA</p>
                    </div>
                </div>
            </div>
        </section>
    </main>
<?php require("components/footer.php");?>
