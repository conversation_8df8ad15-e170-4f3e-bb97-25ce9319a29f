<?php

use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use P<PERSON>Mailer\PHPMailer\Exception;

require 'vendor/autoload.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $name = $_POST['name'];
    $email = $_POST['email'];
    $subject = $_POST['subject'];
    $message = $_POST['message'];

    $mail = new PHPMailer(true);

    try {
        //Server settings
        $mail->isSMTP();
        $mail->Host = 'smtp.gmail.com';
        $mail->SMTPAuth = true;
        $mail->Username = 'YOUR_EMAIL';
        $mail->Password = 'YOUR_APP_PASSWORD';
        $mail->SMTPSecure = 'tls';
        $mail->Port = 587;

        //Recipients
        $mail->setFrom('YOUR_EMAIL', 'Contact Form');
        $mail->addAddress('YOUR_EMAIL');

        // Content
        $mail->isHTML(true);
        $mail->Subject = "New Contact Form Submission: " . $subject;
        $mail->Body = "
            <strong>Name:</strong> $name <br>
            <strong>Email:</strong> $email <br><br>
            <strong>Message:</strong><br> $message
        ";

        $mail->send();
        header('Location:contact.php?success=1');
        exit();
    } catch (Exception $e) {
        header("Location: contact.php?error=1");
        exit();
    }
}
