/* style.css - Custom color scheme override */

:root {
    --primary-color: #1a73e8; /* New primary color */
    --secondary-color: #fbbc05; /* New secondary color */
    --accent-color: #34a853; /* Accent color */
    --background-color: #f5f7fa; /* Background color */
    --text-color: #222222; /* Main text color */
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
}

a, .link {
    color: var(--primary-color);
}

a:hover, .link:hover {
    color: var(--secondary-color);
}

.btn-primary, .btn {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: #fff;
}

.btn-primary:hover, .btn:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: #fff;
}

.header, nav, .navbar {
    background-color: var(--primary-color);
    color: #fff;
}

.footer {
    background-color: var(--secondary-color);
    color: #fff;
}

.card, .panel {
    background-color: #fff;
    border-color: var(--primary-color);
}

/* Add more custom overrides as needed */
